import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { productData } from '../data/productData';
// Import Swiper React components
import { Swiper, SwiperSlide } from 'swiper/react';
// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
// Import required modules
import { Pagination, Navigation } from 'swiper/modules';

interface ProductDetailProps {
  language: 'vi' | 'en';
  product: {
    id: string;
    name: {
      vi: string;
      en: string;
    };
    category: {
      id: string;
      name: {
        vi: string;
        en: string;
      };
    };
    description: {
      vi: string;
      en: string;
    };
    images: string[];
    specifications: {
      composition?: {
        vi: string;
        en: string;
      };
      color?: {
        vi: string;
        en: string;
      };
      califore?: {
        vi: string;
        en: string;
      };
      brix?: {
        vi: string;
        en: string;
      };
      ecoli?: {
        vi: string;
        en: string;
      };
    };
    storageConditions?: {
      vi: string;
      en: string;
    };
    packing?: {
      bulk?: {
        vi: string;
        en: string;
      };
      retail?: {
        vi: string;
        en: string;
      };
    };
    shelfLife?: {
      vi: string;
      en: string;
    };
    harvestSeason?: {
      vi: string;
      en: string;
    };
    nutritionFacts?: {
      calories: number;
      servingSize: string;
      totalFat: number;
      saturatedFat: number;
      transFat: number;
      cholesterol: number;
      sodium: number;
      totalCarbs: number;
      dietaryFiber: number;
      sugars: number;
      protein: number;
      vitaminD: number;
      calcium: number;
      iron: number;
      potassium: number;
    };
  };
}

const ProductDetail = ({ language, product }: ProductDetailProps) => {
  const translations = {
    specifications: {
      vi: 'Thông số kỹ thuật',
      en: 'Specifications'
    },
    composition: {
      vi: 'Thành phần',
      en: 'Composition'
    },
    color: {
      vi: 'Màu sắc',
      en: 'Color'
    },
    califore: {
      vi: 'Califore',
      en: 'Califore'
    },
    brix: {
      vi: 'Brix',
      en: 'Brix'
    },
    ecoli: {
      vi: 'E.coli',
      en: 'E.coli'
    },
    storageConditions: {
      vi: 'Điều kiện bảo quản',
      en: 'Storage Conditions'
    },
    packing: {
      vi: 'Đóng gói',
      en: 'Packing'
    },
    bulk: {
      vi: 'Đóng gói số lượng lớn',
      en: 'Bulk packing'
    },
    retail: {
      vi: 'Đóng gói bán lẻ',
      en: 'Retail packing'
    },
    shelfLife: {
      vi: 'Thời hạn sử dụng',
      en: 'Shelf life'
    },
    harvestSeason: {
      vi: 'Mùa thu hoạch',
      en: 'Harvest season'
    },
    nutritionFacts: {
      vi: 'Thông tin dinh dưỡng',
      en: 'Nutrition Facts'
    },
    servingSize: {
      vi: 'Khẩu phần',
      en: 'Serving Size'
    },
    calories: {
      vi: 'Calo',
      en: 'Calories'
    },
    totalFat: {
      vi: 'Tổng chất béo',
      en: 'Total Fat'
    },
    saturatedFat: {
      vi: 'Chất béo bão hòa',
      en: 'Saturated Fat'
    },
    transFat: {
      vi: 'Chất béo trans',
      en: 'Trans Fat'
    },
    cholesterol: {
      vi: 'Cholesterol',
      en: 'Cholesterol'
    },
    sodium: {
      vi: 'Natri',
      en: 'Sodium'
    },
    totalCarbs: {
      vi: 'Tổng carbohydrate',
      en: 'Total Carbohydrate'
    },
    dietaryFiber: {
      vi: 'Chất xơ',
      en: 'Dietary Fiber'
    },
    sugars: {
      vi: 'Đường',
      en: 'Sugars'
    },
    protein: {
      vi: 'Protein',
      en: 'Protein'
    },
    vitaminD: {
      vi: 'Vitamin D',
      en: 'Vitamin D'
    },
    calcium: {
      vi: 'Canxi',
      en: 'Calcium'
    },
    iron: {
      vi: 'Sắt',
      en: 'Iron'
    },
    potassium: {
      vi: 'Kali',
      en: 'Potassium'
    },
    buyNow: {
      vi: 'MUA NGAY',
      en: 'BUY NOW'
    },

  };

  const [mainImage, setMainImage] = useState(product.images[0]);

  // Cập nhật mainImage khi product.images thay đổi
  useEffect(() => {
    setMainImage(product.images[0]);
  }, [product.images]);

  return (
    <div className="w-full min-h-screen py-12 bg-[url('/images/assets/bg_1.png')] bg-cover bg-center bg-no-repeat">
      {/* <div className='flex justify-center w-full border border-red-200 '>
      <h1 className="text-4xl font-bold text-[rgb(19,104,174)] mb-4 text-center">
            {product.name[language]}
          </h1>

      </div> */}
      <div className="flex flex-col md:flex-row ml-[7%] mr-[7%] md:items-start">
        {/* Product Images */}
        <div className="md:w-1/4 flex justify-center">
          <div className="bg-white rounded-[20px] shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 w-full h-[500px] flex flex-col">
            <div className="overflow-hidden flex-shrink-0">
              <img
                src={mainImage}
                alt={product.name[language]}
                className="w-full h-[300px] object-cover hover:scale-105 transition-transform duration-500"
              />
            </div>
            <div className="flex space-x-2 overflow-x-auto p-2 justify-center flex-shrink-0 ">
              {product.images.map((image, index) => (
                <div
                  key={index}
                  className={`w-20 h-20 rounded-[10px] cursor-pointer border-2 ${mainImage === image ? 'border-[rgb(19,104,174)]' : 'border-gray-200'} hover:border-[rgb(19,104,174)] transition-all duration-300 overflow-hidden shadow-md`}
                  onClick={() => setMainImage(image)}
                >
                  <img
                    src={image}
                    alt={`${product.name[language]} ${index + 1}`}
                    className="w-full h-full object-cover rounded-[8px] hover:scale-110 transition-transform duration-300"
                  />
                </div>
              ))}
            </div>

            {/* Certifications */}
            <div className="flex justify-center space-x-4 bg-white p-4 shadow-inner mt-auto flex-shrink-0">
              <img src="/images/chungchi/HACCP.jpg" alt="HACCP" className="h-16 hover:scale-110 transition-transform duration-300" />
              <img src="/images/chungchi/ISO.jpg" alt="ISO" className="h-16 hover:scale-110 transition-transform duration-300" />
              <img src="/images/chungchi/KOSHER.jpg" alt="KOSHER" className="h-16 hover:scale-110 transition-transform duration-300" />
              <img src="/images/chungchi/USDA.jpg" alt="USDA" className="h-16 hover:scale-110 transition-transform duration-300" />
            </div>
          </div>
        </div>

        {/* Product Info */}
        <div className="md:w-1/2 md:px-10  text-Justify ">
          <h1 className="text-4xl font-bold text-[rgb(19,104,174)] mb-2 text-center ">
            {product.name[language]}
          </h1>

          <p className="text-black mb-3 text-justify font-momo">
            {product.description[language]}
          </p>

          {/* Specifications */}
          <div className="mb-1 text-sm">
            <div className="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[rgb(19,104,174)] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <h2 className="text-x font-semibold mb-3">
                {translations.specifications[language]}:
              </h2>
            </div>
            <ul className="space-y-1">
              {product.specifications.composition && (
                <li className="flex items-start ml-8">
                  <span className="text-[rgb(19,104,174)] mr-2">•</span>
                  <span className="font-medium mr-2">{translations.composition[language]}:</span>
                  <span>{product.specifications.composition[language]}</span>
                </li>
              )}
              {product.specifications.color && (
                <li className="flex items-start  ml-8">
                  <span className="text-[rgb(19,104,174)] mr-2">•</span>
                  <span className="font-medium mr-2">{translations.color[language]}:</span>
                  <span>{product.specifications.color[language]}</span>
                </li>
              )}
              {/* {product.specifications.califore && (
                <li className="flex items-start">
                  <span className="text-[rgb(19,104,174)] mr-2">•</span>
                  <span className="font-medium mr-2">{translations.califore[language]}:</span>
                  <span>{product.specifications.califore[language]}</span>
                </li>
              )}
              {product.specifications.brix && (
                <li className="flex items-start">
                  <span className="text-[rgb(19,104,174)] mr-2">•</span>
                  <span className="font-medium mr-2">{translations.brix[language]}:</span>
                  <span>{product.specifications.brix[language]}</span>
                </li>
              )}
              {product.specifications.ecoli && (
                <li className="flex items-start">
                  <span className="text-[rgb(19,104,174)] mr-2">•</span>
                  <span className="font-medium mr-2">{translations.ecoli[language]}:</span>
                  <span>{product.specifications.ecoli[language]}</span>
                </li>
              )} */}
            </ul>
          </div>

          {/* Storage Conditions */}
          {product.storageConditions && (
            <div className="mb-2 text-sm">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[rgb(19,104,174)] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                <div>
                  <span className="font-medium">{translations.storageConditions[language]}:</span> {product.storageConditions[language]}
                </div>
              </div>
            </div>
          )}

          {/* Packing */}
          {product.packing && (
            <div className="mb-2 text-sm">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-[rgb(19,104,174)] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                </svg>
                <div>
                  <span className="font-medium">{translations.packing[language]}:</span>
                  <ul className=" mt-1">
                    {product.packing.bulk && (

                      <li>
                        <span className="text-[rgb(19,104,174)] mr-2">•</span>
                        {translations.bulk[language]}: {product.packing.bulk[language]}
                        </li>
                    )}
                    {product.packing.retail && (
                      <li>
                        <span className="text-[rgb(19,104,174)] mr-2">•</span>
                        {translations.retail[language]}: {product.packing.retail[language]}
                        </li>
                    )}

                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Shelf Life */}
          {product.shelfLife && (
            <div className="mb-2 text-sm">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[rgb(19,104,174)] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <span className="font-medium">{translations.shelfLife[language]}:</span> {product.shelfLife[language]}
                </div>
              </div>
            </div>
          )}

          {/* Harvest Season */}
          {product.harvestSeason && (
            <div className="mb-2 text-sm">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[rgb(19,104,174)] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <div>
                  <span className="font-medium">{translations.harvestSeason[language]}:</span> {product.harvestSeason[language]}
                </div>
              </div>
            </div>
          )}
{/*
          <div className="flex justify-center">
            <button className="bg-[rgb(19,104,174)] text-white font-bold py-3 px-8 rounded-md hover:bg-[rgb(15,83,139)] transition duration-300 w-1/2">
              {translations.buyNow[language]}
            </button>
          </div> */}
        </div>

        {/* Nutrition Facts */}
        {product.nutritionFacts && (
          <div className="md:w-1/4 flex justify-center">
            <div className="border-8 border-[rgb(19,104,174)] p-2 w-full max-w-[300px] h-[500px] rounded-lg shadow-md overflow-y-auto">
              <div className="text-sm font-bold border-b-2 border-[rgb(19,104,174)] pb-0.5">
                {translations.nutritionFacts[language]}
              </div>
              <div className="text-xs border-b-4 border-[rgb(19,104,174)] py-0.5">
                {translations.servingSize[language]}: {product.nutritionFacts.servingSize}
              </div>
              <div className="border-b-6 border-[rgb(19,104,174)] py-1">
                <div className="font-bold text-xl">{translations.calories[language]} {product.nutritionFacts.calories}</div>
              </div>
              <div className="border-b-4 border-[rgb(19,104,174)] py-0.5 text-right font-bold text-xs">% Daily Value*</div>

              <div className="flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs">
                <div><strong>{translations.totalFat[language]}</strong> {product.nutritionFacts.totalFat}g</div>
                <div className="font-bold">{Math.round((product.nutritionFacts.totalFat / 78) * 100)}%</div>
              </div>

              <div className="flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 pl-2 text-xs">
                <div>{translations.saturatedFat[language]} {product.nutritionFacts.saturatedFat}g</div>
                <div className="font-bold">{Math.round((product.nutritionFacts.saturatedFat / 20) * 100)}%</div>
              </div>

              <div className="flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 pl-2 text-xs">
                <div>{translations.transFat[language]} {product.nutritionFacts.transFat}g</div>
                <div></div>
              </div>

              <div className="flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs">
                <div><strong>{translations.cholesterol[language]}</strong> {product.nutritionFacts.cholesterol}mg</div>
                <div className="font-bold">{Math.round((product.nutritionFacts.cholesterol / 300) * 100)}%</div>
              </div>

              <div className="flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs">
                <div><strong>{translations.sodium[language]}</strong> {product.nutritionFacts.sodium}mg</div>
                <div className="font-bold">{Math.round((product.nutritionFacts.sodium / 2300) * 100)}%</div>
              </div>

              <div className="flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs">
                <div><strong>{translations.totalCarbs[language]}</strong> {product.nutritionFacts.totalCarbs}g</div>
                <div className="font-bold">{Math.round((product.nutritionFacts.totalCarbs / 275) * 100)}%</div>
              </div>

              <div className="flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 pl-2 text-xs">
                <div>{translations.dietaryFiber[language]} {product.nutritionFacts.dietaryFiber}g</div>
                <div className="font-bold">{Math.round((product.nutritionFacts.dietaryFiber / 28) * 100)}%</div>
              </div>

              <div className="flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 pl-2 text-xs">
                <div>{translations.sugars[language]} {product.nutritionFacts.sugars}g</div>
                <div></div>
              </div>

              <div className="flex justify-between border-b-4 border-[rgb(19,104,174)] py-0.5 text-xs">
                <div><strong>{translations.protein[language]}</strong> {product.nutritionFacts.protein}g</div>
                <div></div>
              </div>

              <div className="flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs">
                <div>{translations.vitaminD[language]} {product.nutritionFacts.vitaminD}mcg</div>
                <div className="font-bold">{Math.round((product.nutritionFacts.vitaminD / 20) * 100)}%</div>
              </div>

              <div className="flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs">
                <div>{translations.calcium[language]} {product.nutritionFacts.calcium}mg</div>
                <div className="font-bold">{Math.round((product.nutritionFacts.calcium / 1300) * 100)}%</div>
              </div>

              <div className="flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs">
                <div>{translations.iron[language]} {product.nutritionFacts.iron}mg</div>
                <div className="font-bold">{Math.round((product.nutritionFacts.iron / 18) * 100)}%</div>
              </div>

              <div className="flex justify-between border-b-4 border-[rgb(19,104,174)] py-0.5 text-xs">
                <div>{translations.potassium[language]} {product.nutritionFacts.potassium}mg</div>
                <div className="font-bold">{Math.round((product.nutritionFacts.potassium / 4700) * 100)}%</div>
              </div>

              <div className="text-[10px] mt-1 text-justify">
                * The % Daily Value (DV) tells you how much a nutrient in a serving of food contributes to a daily diet. 2,000 calories a day is used for general nutrition advice.
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="flex justify-center px-4">
            <button className="bg-[rgb(19,104,174)] text-white font-bold py-3 px-8 rounded-md hover:bg-[rgb(15,83,139)] transition duration-300 w-full max-w-xs sm:w-auto sm:min-w-[200px] whitespace-nowrap">
              {translations.buyNow[language]}
            </button>
      </div>

      {/* Related Products */}
      <div className="mt-8 mb-8 ml-[10%] mr-[10%]">
        <h2 className="text-2xl font-bold text-[rgb(19,104,174)] mb-6 text-center">
          {language === 'vi' ? 'Sản phẩm đề xuất' : 'Recommended Products'}
        </h2>

        {/* Swiper Carousel */}
        <div className="relative px-[40px] recommended-products-container">
          {/* Custom navigation buttons */}
          <div className="swiper-button-prev custom-swiper-button-prev"></div>
          <div className="swiper-button-next custom-swiper-button-next"></div>

          <Swiper
            slidesPerView={1}
            spaceBetween={15}
            pagination={{
              clickable: true,
              dynamicBullets: true,
            }}
            navigation={{
              nextEl: '.custom-swiper-button-next',
              prevEl: '.custom-swiper-button-prev',
            }}
            modules={[Pagination, Navigation]}
            breakpoints={{
              640: {
                slidesPerView: 2,
                spaceBetween: 15,
              },
              768: {
                slidesPerView: 3,
                spaceBetween: 15,
              },
              1024: {
                slidesPerView: 4,
                spaceBetween: 15,
              },
            }}
            loop={true}
            className="mySwiper"
          >
            {productData
              .filter(p =>
                // Lọc các sản phẩm cùng danh mục nhưng khác ID với sản phẩm hiện tại
                p.category.id === product.category.id && p.id !== product.id
              )
              .map((relatedProduct) => (
                <SwiperSlide key={relatedProduct.id}>
                  <div className="bg-white rounded-[20px] shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 h-full">
                    <div className="h-48 overflow-hidden">
                      <img
                        src={relatedProduct.images[0]}
                        alt={relatedProduct.name[language]}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-semibold text-[rgb(19,104,174)] mb-2 text-center">
                        {relatedProduct.name[language]}
                      </h3>
                      <p className="text-sm text-gray-600 mb-3 text-center">
                        {relatedProduct.specifications.composition?.[language] || ''}
                      </p>
                      <Link to={`/products/${relatedProduct.category.id}/${relatedProduct.id}`}>
                        <button className="bg-[rgb(19,104,174)] text-white text-sm py-2 px-4 rounded-md hover:bg-[rgb(15,83,139)] transition duration-300 w-full">
                          {language === 'vi' ? 'Xem chi tiết' : 'View details'}
                        </button>
                      </Link>
                    </div>
                  </div>
                </SwiperSlide>
              ))}
          </Swiper>
        </div>
      </div>

    </div>
  );
};

export default ProductDetail;
